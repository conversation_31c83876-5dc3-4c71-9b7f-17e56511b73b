<script lang="ts" setup>
import ChatHeader from '@/components/common/ChatHeader.vue';
import WelcomeLogo from '@/components/common/WelcomeLogo.vue';
import { ConversationType } from '@/constants/enums';
import {
  ChatService,
  type ChatMessage
} from '@/services/chatService';
import { useChatStore } from '@/stores/chat';
import { useConversationStore } from '@/stores/conversation';
import { useMedicalRecordStore } from '@/stores/medicalRecord';
import { useSliderStore } from '@/stores/slider';
import { useVoiceStore } from '@/stores/voice';
import { SpeechServiceValidator } from '@/utils/speechServiceValidator';
import { ElMessage } from 'element-plus';
import { storeToRefs } from 'pinia';
import { computed, onMounted } from 'vue';
import BubbleList from './components/BubbleList.vue';
import ChatInput from './components/ChatInput.vue';
import VoiceInput from './components/VoiceInput.vue';
import VoiceMedicalRecord from './components/VoiceMedicalRecord.vue';
import VoiceText from './components/VoiceText.vue';

// 全局Pina库
const chatStore = useChatStore();
const voiceStore = useVoiceStore();
const sliderStore = useSliderStore();
const conversationStore = useConversationStore();
const medicalRecordStore = useMedicalRecordStore();

// 全局响应数据
const {
  messageList,
  isAiTyping
} = storeToRefs(chatStore);
const { curConversation } = storeToRefs(sliderStore);
const {
  recognitionText
} = storeToRefs(voiceStore);
const {
  medicalRecord
} = storeToRefs(medicalRecordStore);

// 全局方法
const { addConversation } = conversationStore;

// 本组件的响应数据
const showVoiceText = computed(() => {
  return !!recognitionText && messageList.value.length === 0
})
const showBubbleList = computed(() => {
  return curConversation.value && messageList.value.length > 0
})

// 本组件的方法
function addErrorMessage(msg: string = "输出已中断，请重新发送消息"): void {
  const lastAiMessage = messageList.value
    .slice()
    .reverse()
    .find((msg) => msg.type === "ai");
  if (lastAiMessage) {
    lastAiMessage.loading = false;
    lastAiMessage.thinkLoading = false;
    if (!lastAiMessage.content) {
      lastAiMessage.content = msg;
    }
  }
}
const addMessage = (
  messageId: number,
  message: string,
  type: "user" | "ai" = "user"
): ChatMessage => {
  const newMessage: ChatMessage = {
    id: messageId,
    type,
    content: message,
    timestamp: Date.now(),
    loading: type === "ai",
    thinkLoading: type === "ai",
  };
  messageList.value.push(newMessage);
  return newMessage;
};
const updateMessage = (
  messageId: number,
  content: string,
  thinking?: string,
  finished?: boolean,
  thinkFinished?: boolean
): void => {
  const messageIndex = messageList.value.findIndex(
    (msg) => msg.id === messageId
  );
  if (messageIndex !== -1) {
    const message = messageList.value[messageIndex];

    const updates: Partial<typeof message> = {};

    if (content !== message.content) {
      updates.content = content;
    }
    if (thinking !== undefined && thinking !== message.thinking) {
      updates.thinking = thinking;
    }
    if (finished !== undefined && message.loading !== !finished) {
      updates.loading = !finished;
    }
    if (
      thinkFinished !== undefined &&
      message.thinkLoading !== !thinkFinished
    ) {
      updates.thinkLoading = !thinkFinished;
    }

    if (Object.keys(updates).length > 0) {
      Object.assign(message, updates);
    }
  }
};
const handleSendMessage = async (messageContent?: string, voiceMedicalRecordId?: number): Promise<void> => {
  const userMessage = messageContent || "";
  let curConversationId: string;

  if (!userMessage.trim()) {
    ElMessage.warning("请输入消息内容");
    return;
  }
  if (isAiTyping.value) {
    ElMessage.warning("AI正在回复中，请稍候...");
    return;
  }

  if (!!curConversation.value) {
    curConversationId = curConversation.value.conversation_id;
  } else {
    const newConversation = await addConversation(
      userMessage.slice(0, 20),
      ConversationType.VOICE,
      voiceMedicalRecordId
    );
    curConversationId = newConversation.conversation_id;
    curConversation.value = newConversation;
  }
  addMessage(
    Date.now() + Math.random(),
    userMessage,
    "user"
  );
  isAiTyping.value = true;
  try {
    await ChatService.sendMessageStream(
      {
        message: userMessage,
        conversationId: curConversationId,
      },
      (chunk) => {
        // 更新消息内容
        updateMessage(
          chunk.messageId,
          chunk.content,
          chunk.thinking,
          chunk.finished,
          chunk.thinkFinished
        );
      },
      (messageId) => {
        addMessage(messageId, "", "ai");
      }
    );
  } catch (error) {
    addErrorMessage("抱歉，我现在无法回复您的消息，请稍后再试。");
  } finally {
    isAiTyping.value = false;
  }
};
const handleSendMessageFromVoiceText = async (voiceMedicalRecordId: number) => {
  if (voiceMedicalRecordId) {
    await handleSendMessage("根据宠物病历生成宠物建议", voiceMedicalRecordId);
  }
};

// 组件挂载时验证语音服务配置
onMounted(() => {
  // 验证当前使用的语音服务
  const serviceStatus = SpeechServiceValidator.validateCurrentService();

  if (serviceStatus.isCustomService) {
    console.log('✅ VoiceView: 已成功切换到自定义语音识别服务');
    console.log('🔗 WebSocket URL:', serviceStatus.wsUrl);
  } else {
    console.log('🔵 VoiceView: 当前使用阿里云语音识别服务');
  }

  // 在开发环境下显示详细状态
  if (import.meta.env.DEV) {
    ElMessage.success({
      message: serviceStatus.isCustomService
        ? '已切换到自定义语音识别服务'
        : '当前使用阿里云语音识别服务',
      duration: 3000
    });
  }
});
</script>

<template>
  <ChatHeader />
  <div class="page-container" :class="{ 'show-bubble-page-container': showBubbleList && medicalRecord }">
    <div class="page-left" :class="{
      'recognition-text': showVoiceText,
      'medical-record': (showBubbleList && medicalRecord)
    }">
      <BubbleList v-if="showBubbleList" />
      <WelcomeLogo v-else />
      <ChatInput v-if="showBubbleList" :handle-send-message="handleSendMessage" :is-ai-typing="isAiTyping"
        :class="{ 'fly-chat-input': showBubbleList }" />
      <VoiceInput v-else />
    </div>
    <div v-if="(showBubbleList && medicalRecord) || (!!recognitionText && messageList.length === 0)" class="page-right"
      :class="{
        'recognition-text': showVoiceText,
        'medical-record': (showBubbleList && medicalRecord)
      }">
      <VoiceMedicalRecord v-if="showBubbleList && medicalRecord" />
      <VoiceText v-if="!!recognitionText && messageList.length === 0" @send-message="handleSendMessageFromVoiceText" />
    </div>
  </div>
</template>
<style scoped src="./index.css"></style>

/* 基础容器样式 */
.bubble-list-container {
  width: 100%;
}

.bubble-list-container .el-bubble-list {
  height: 90dvh;
}

.bubble-list-container .el-bubble-list::after {
  content: "占位符";
  display: block;
  width: 100%;
  line-height: 25dvh;
  opacity: 0;
}

/* 数据加载样式 */
.bubble-list-container .el-bubble-list::before {
  content: "数据加载中...";
  display: none;
  width: 100%;
  text-align: center;
  font-size: var(--el-font-size-base);
  color: var(--el-color-info);
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

.bubble-list-container.have-more-message .el-bubble-list::before {
  display: block;
  opacity: 1;
}

/* 头像样式 */
:deep(.bubble-list .el-avatar) {
  background: linear-gradient(315deg, #a0d8f0 0%, #80b3ff 100%);
}

/* 隐藏气泡占位符 */
:deep(.bubble-list .el-bubble.el-bubble-end .el-bubble-avatar-placeholder) {
  display: none;
}

/* 消息气泡中h1样式修改 */
:deep(.bubble-list .el-bubble-content.el-bubble-content-filled h1) {
  font-size: 1.6em;
}

:deep(.bubble-list .el-bubble-content.el-bubble-content-filled h2) {
  font-size: 1.35em;
}

/* AI消息气泡基础样式 */
:deep(
  .bubble-list
    .el-bubble.el-bubble-start
    .el-bubble-content.el-bubble-content-filled
) {
  background-color: #ffffff;
  border-radius: 12px;
  padding-block: 0px;
}

/* 思考过程相关样式 */
:deep(.thinking-header) {
  border-bottom: 1px solid rgba(135, 206, 235, 0.3);
}

:deep(.thinking-collapsed .thinking-header) {
  border-bottom: none;
}

.thinking-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.thinking-collapse-button {
  padding: 6px;
  margin-left: 8px;
  color: var(--el-color-info);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 4px;
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.thinking-collapse-button:hover {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.thinking-collapse-button svg {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.thinking-collapse-button:hover svg {
  transform: scale(1.1);
}

.thinking-collapse-button:active {
  transform: scale(0.95);
}

/* 思考过程容器样式 */
.thinking-container {
  margin-bottom: 12px;
  border: 1px solid rgba(135, 206, 235, 0.3);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  overflow: hidden;
  transition: all 0.3s ease;
}

.thinking-container.thinking-collapsed {
  max-width: none;
}

.thinking-container:hover {
  border-color: rgba(135, 206, 235, 0.5);
  box-shadow: 0 4px 12px rgba(96, 151, 252, 0.2);
}

.thinking-content {
  line-height: 1.6;
  color: #666;
  white-space: pre-wrap;
  word-break: break-word;
  background: rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  padding: 24px;
  font-size: 14px;
}

/* 简单的折叠动画 */
.thinking-collapse-enter-active,
.thinking-collapse-leave-active {
  transition: all 0.3s ease;
  overflow: hidden;
}

.thinking-collapse-enter-from,
.thinking-collapse-leave-to {
  max-height: 0;
  opacity: 0;
  transform: translateY(-10px);
}

.thinking-collapse-enter-to,
.thinking-collapse-leave-from {
  max-height: 500px;
  opacity: 1;
  transform: translateY(0);
}

:deep(.thinking-body) {
  padding: 0;
}

/* 思考过程头部样式增强 */
.thinking-header-content span {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

/* 当只有思考过程时隐藏气泡内容 */
.thinking-only :deep(.el-bubble-content) {
  display: none !important;
}

.thinking-only :deep(.el-bubble-content-filled) {
  display: none !important;
}

/* Footer 按钮样式 */
.footer-container {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.footer-buttons {
  display: flex;
  gap: 8px;
}

.footer-button {
  padding: 4px 8px;
  min-width: 32px;
  height: 28px;
  border-radius: 6px;
  color: var(--el-color-info);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

/* 复制按钮默认颜色 */
.footer-button.copy-button {
  color: #3b82f6;
}

/* 点赞按钮默认颜色 */
.footer-button.like-button {
  color: #f59e0b;
}

/* 踩按钮默认颜色 */
.footer-button.dislike-button {
  color: #ef4444;
}

.footer-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.footer-button:active {
  transform: translateY(0);
}

/* 复制按钮悬停效果 */
.footer-button.copy-button:hover {
  background-color: rgba(59, 130, 246, 0.1);
  color: #2563eb;
}

/* 点赞按钮悬停效果 */
.footer-button.like-button:hover {
  background-color: rgba(245, 158, 11, 0.1);
  color: #d97706;
}

/* 踩按钮悬停效果 */
.footer-button.dislike-button:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

/* 已点赞状态的按钮样式 */
.footer-button.like-button.liked {
  color: #d97706;
  background-color: rgba(245, 158, 11, 0.1);
}

.footer-button.like-button.liked:hover {
  color: #b45309;
  background-color: rgba(245, 158, 11, 0.2);
}

/* 已踩状态的按钮样式 */
.footer-button.dislike-button.disliked {
  color: #dc2626;
  background-color: rgba(239, 68, 68, 0.1);
}

.footer-button.dislike-button.disliked:hover {
  color: #b91c1c;
  background-color: rgba(239, 68, 68, 0.2);
}

.footer-remarks {
  font-size: 12px;
  color: rgb(255, 179, 0);
  display: flex;
  align-items: center;
}

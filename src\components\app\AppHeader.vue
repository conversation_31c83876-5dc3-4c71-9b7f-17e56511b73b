<script lang="ts" setup>
import LoginIcon from "@/components/icons/LoginIcon.vue";
import LogoutIcon from "@/components/icons/LogoutIcon.vue";
import { useCommonStore } from "@/stores/common";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { ref } from "vue";

const router = useRouter();
const commonStore = useCommonStore();

// 多语言选择
const currentLanguage = ref('zh-CN');
const languageOptions = [
  { label: '中文', value: 'zh-CN' },
  { label: 'English', value: 'en-US' }
];

const handleLanguageChange = (value: string) => {
  currentLanguage.value = value;
  // 这里可以添加实际的语言切换逻辑
  console.log('Language changed to:', value);
};

const goToHome = () => {
  router.push("/");
};

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm("确定要退出登录吗？", "退出确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    // 使用 store 的退出方法（会清除 token 和更新登录状态）
    commonStore.logout();
    // 显示成功提示
    ElMessage.success("退出登录成功");
  } catch (error) {
    // 用户取消退出，不做任何操作
  }
};

// 显示登录弹窗
const showLoginDialog = () => {
  commonStore.showLogin = true;
};

// 下拉菜单点击处理
const handleCommand = (command: string) => {
  switch (command) {
    case "logout":
      handleLogout();
      break;
    case "login":
      showLoginDialog();
      break;
    default:
      break;
  }
};
</script>

<template>
  <div class="app-header">
    <img src="/text_logo_cn.png" alt="logo" @click="goToHome" />
    <nav class="navbar">
      <div class="nav-links">
        <a v-if="router.currentRoute.value.path === '/chat'" href="/"
          >首页</a
        >
        <a v-else href="/chat">开始使用</a>
        <a href="/vet-assistant">宠物助手</a>
        <a href="/vet-open">开放平台</a>
        <a href="/vet-open">晓闻官网</a>
        <a href="#">关于我们</a>
      </div>
    </nav>
    <div class="app-header-user">
      <!-- 多语言切换下拉框 -->
      <div class="language-selector">
        <el-select 
          v-model="currentLanguage" 
          @change="handleLanguageChange"
          size="small"
        >
          <el-option
            v-for="item in languageOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      
      <!-- 用户头像下拉菜单（登录和未登录状态都显示） -->
      <el-dropdown
        @command="handleCommand"
        trigger="click"
        placement="bottom-end"
        class="user-dropdown"
      >
        <el-avatar
          src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"
          class="user-avatar"
        />
        <template #dropdown>
          <el-dropdown-menu class="user-dropdown-menu">
            <el-dropdown-item
              v-if="commonStore.isLogin"
              command="logout"
              class="logout-item"
            >
              <LogoutIcon />
              <span style="margin-left: 0.5dvw">退出登录</span>
            </el-dropdown-item>
            <el-dropdown-item v-else command="login" class="login-item">
              <LoginIcon />
              <span style="margin-left: 0.5dvw">登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<style scoped src="./AppHeader.css"></style>

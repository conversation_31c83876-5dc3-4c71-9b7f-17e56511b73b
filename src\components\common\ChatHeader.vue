<script lang="ts" setup>
import { LANGUAGE_OPTIONS } from "@/constants/constant";
import { useCommonStore } from "@/stores/common";
import { useSliderStore } from "@/stores/slider";
import { ElDropdown, ElDropdownItem, ElDropdownMenu } from "element-plus";
import { storeToRefs } from "pinia";
import { onMounted } from "vue";
import { useI18n } from "vue-i18n";

// 全局的Pina库
const sliderStore = useSliderStore();
const commonStore = useCommonStore();
const { locale, t } = useI18n();

// 全局的响应数据
const { curFunction } = storeToRefs(sliderStore);
const { currentLanguageInfo } = storeToRefs(commonStore);

// 组件挂载时设置浏览器默认语言
onMounted(() => {
  commonStore.initializeLanguage();
});
</script>

<template>
  <div class="header-container">
    <div class="header-left"></div>
    <div class="header-center">
      {{ t(curFunction.title) }}
    </div>
    <div class="header-right">
      <el-dropdown class="language-dropdown" trigger="click" @command="commonStore.handleLanguageChange">
        <div class="language-selector">
          <svg class="globe-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"
              fill="currentColor" />
          </svg>
          <span class="language-text">{{ currentLanguageInfo.label }}</span>
          <svg class="dropdown-arrow" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 10L12 15L17 10H7Z" fill="currentColor" />
          </svg>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="language-dropdown-menu">
            <el-dropdown-item v-for="option in LANGUAGE_OPTIONS" :key="option.value" :command="option.value"
              :class="{ active: option.value === locale }" class="language-option">
              <span :class="['fi', `fi-${option.flag}`]"></span>
              <span class="label">{{ option.label }}</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>
<style scoped src="./ChatHeader.css"></style>
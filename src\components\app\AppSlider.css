/* 侧边栏容器 */
.slider {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 13dvw;
  color: #333;
  background-color: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.08);
}

.slider.collapsed {
  width: 3dvw;
  min-width: 3dvw;
}

/* 新对话按钮样式 */
.new-chat {
  padding: 0.3dvw 1dvw;
}

.slider.collapsed .new-chat {
  padding: 6px;
  display: flex;
  justify-content: center;
}

.slider.collapsed .new-chat {
  padding: 6px;
}

.new-chat-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.6dvw;
  width: 100%;
  padding-block: 0.4dvw;
  font-size: 0.7dvw;
  font-weight: 500;
  color: white;
  border: 1px solid rgba(200, 200, 200, 0.3);
  border-radius: 2dvw;
  background: linear-gradient(
    135deg,
    rgb(135, 206, 235) 0%,
    rgb(96, 151, 252) 100%
  );
  backdrop-filter: blur(10px);
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(160, 160, 160, 0.1);
}

.slider.collapsed .new-chat-button {
  padding: 0;
  gap: 0;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.new-chat-button:hover {
  background: linear-gradient(135deg, #95d5f0 0%, #7aa8fd 100%);
  border-color: rgba(135, 206, 235, 0.5);
  box-shadow: 0 4px 12px rgba(96, 151, 252, 0.3);
}

.new-chat-button:active {
  background: linear-gradient(135deg, #6bb6ea 0%, #4a88f7 100%);
  border-color: rgba(96, 151, 252, 0.6);
  box-shadow: 0 0.3dvw 10.3dvw rgba(96, 151, 252, 0.4);
}

.new-chat-button:active:hover {
  background: linear-gradient(135deg, #5aa3e0 0%, #3a6eef 100%);
  border-color: rgba(96, 151, 252, 0.7);
  box-shadow: 0 8px 20px rgba(96, 151, 252, 0.5);
}

.slider-logo {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5dvw 0.5dvw;
  box-sizing: border-box;
}

.slider.collapsed .slider-logo {
  justify-content: center;
  padding: 6px;
}

.collapse-button-container {
  display: flex;
  justify-content: center;
  padding: 6px;
}

.slider-logo-img {
  width: 70%;
  cursor: pointer;
}

.slider-logo-img-collapsed {
  width: 32px;
  height: 32px;
  cursor: pointer;
}

/* 标题样式 */
.slider h3 {
  margin-block: 0 0.6dvw;
  font-size: 0.7dvw;
}

/* 功能模块区域 */
.slider-function {
  flex: 0 0 auto;
  padding: 0.4dvw 1dvw;
}

.slider.collapsed .slider-function {
  padding: 0.4dvw 0.5dvw;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.new-chat-icon {
  font-size: 0.8dvw;
  cursor: pointer;
  padding: 0.3dvw;
  border-radius: 0.4dvw;
  transition: all 0.2s ease;
  margin-right: 0.5dvw;
}

.new-chat-icon:hover {
  background: linear-gradient(
    135deg,
    rgba(200, 200, 200, 0.2) 0%,
    rgba(160, 160, 160, 0.15) 100%
  );
  box-shadow: 0 2px 8px rgba(160, 160, 160, 0.2);
}

/* 功能列表 */
.function-list {
  list-style: none;
  padding-left: 0;
  margin: 0;
}

.function-list li {
  padding: 0.3dvw;
  font-size: 0.7dvw;
  line-height: 0.9dvw;
  margin-bottom: 0.3dvw;
  display: flex;
  align-items: center;
  gap: 0.6dvw;
  border-radius: 0.4dvw;
  transition: all 0.2s ease;
  cursor: pointer;
}

.function-item-content {
  display: flex;
  align-items: center;
  gap: 0.6dvw;
  width: 100%;
}

.slider.collapsed .function-list li {
  justify-content: center;
  padding: 0;
  gap: 0;
  background: transparent;
  margin: 0 auto 1dvw auto;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  border-radius: 6px;
}

.slider.collapsed .function-item-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.slider.collapsed .function-list li:hover {
  background: #edf4ff;
}

.slider.collapsed .function-list li.active {
  background: #edf4ff;
}

.function-list li:last-child {
  margin-bottom: 0;
}

.function-list li:hover {
  background: #edf4ff;
}

.function-list li.active {
  background: #edf4ff;
  box-shadow: 0 2px 12px rgba(160, 160, 160, 0.3);
}

.function-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  --icon-size: 1.4dvw;
}

.slider.collapsed .function-icon {
  --icon-size: 25px;
}

.function-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 历史对话区域 */
.slider-history {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0.3dvw 0.1dvw 0.3dvw 1dvw;
}

.history-list {
  flex: 1 1 0;
  min-height: 0;
  list-style: none;
  padding-left: 0;
  margin: 0;
  overflow-y: auto;
  padding-right: 0.5dvw;
}

/* 加载图标容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5dvw 0 1dvw;
  font-size: 0.5dvw;
  color: var(--el-color-info-light-3);
  gap: 0.5dvw;
}

.loading-icon {
  font-size: 0.6dvw;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.history-list li {
  padding: 0.2dvw;
  font-size: 0.7dvw;
  line-height: 0.8dvw;
  margin-bottom: 0.2dvw;
  border: 0.15dvw solid transparent;
  background-origin: border-box;
  background-clip: padding-box, border-box;
  box-sizing: border-box;
}

.history-list li:hover {
  border: 0.15dvw solid transparent;
  border-radius: 0.4dvw;
  background: #edf4ff;
  cursor: pointer;
}

/* 历史对话选中状态样式 */
.history-list li.active {
  border: 0.15dvw solid transparent;
  border-radius: 0.4dvw;
  background: #edf4ff;
  cursor: pointer;
}

/* 历史对话选中状态的hover样式 */
.history-list li.active:hover {
  border: 0.15dvw solid transparent;
  background: #edf4ff;
}

/* 对话项布局 */
.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.3dvw;
}

.conversation-content {
  flex: 1;
  min-width: 0;
  cursor: pointer;
}

.conversation-title {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 选中状态下的标题样式 */
.conversation-item.active .conversation-title {
  color: rgba(51, 51, 51, 0.95);
  font-weight: 500;
}

/* 重命名输入框 */
.rename-input {
  width: 100%;
}

.rename-input :deep(.el-input__wrapper) {
  background: linear-gradient(
    135deg,
    rgba(200, 200, 200, 0.15) 0%,
    rgba(160, 160, 160, 0.1) 100%
  );
  border: 1px solid rgba(200, 200, 200, 0.4);
  border-radius: 0.2dvw;
  padding: 0.1dvw 0.3dvw;
  backdrop-filter: blur(5px);
}

.rename-input :deep(.el-input__inner) {
  color: #333;
  font-size: 0.7dvw;
  line-height: 0.8dvw;
}

/* 三点菜单按钮 */
.conversation-menu {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.conversation-item:hover .conversation-menu {
  opacity: 1;
}

.menu-button {
  padding: 0.2dvw;
  color: rgba(51, 51, 51, 0.6);
  outline: 0px solid transparent;
  background: transparent;
  font-size: 0.6dvw;
  min-height: auto;
  height: auto;
}

.menu-button:hover {
  color: rgba(51, 51, 51, 0.9);
  background: linear-gradient(
    135deg,
    rgba(200, 200, 200, 0.2) 0%,
    rgba(160, 160, 160, 0.15) 100%
  );
  outline: 1px solid rgba(200, 200, 200, 0.3);
}

/* 下拉菜单样式 */
.conversation-dropdown-menu {
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(200, 200, 200, 0.1) 100%
  );
  backdrop-filter: blur(15px);
  border: 1px solid rgba(200, 200, 200, 0.3);
  border-radius: 0.4dvw;
  box-shadow: 0 4px 10.3dvw rgba(160, 160, 160, 0.15);
  min-width: 5dvw;
}

.conversation-dropdown-menu .el-dropdown-menu__item {
  display: flex;
  align-items: center;
  padding: 0.4dvw 0.6dvw;
  font-size: 0.6dvw;
  color: #333;
}

.conversation-dropdown-menu .el-dropdown-menu__item:hover {
  background: linear-gradient(
    135deg,
    rgba(200, 200, 200, 0.2) 0%,
    rgba(160, 160, 160, 0.15) 100%
  );
}

.rename-item {
  color: #f56c6c !important;
}

.delete-item {
  color: #666 !important;
}

/* 底部区域 */
.slider-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.3dvw 0.5dvw 0.3dvw 1dvw;
  border-top: 1px solid rgba(200, 200, 200, 0.2);
  margin-top: auto;
  /* 确保底部区域在最下面 */
}

.slider.collapsed .slider-footer {
  flex-direction: column;
  gap: 0.5dvw;
  padding: 0.3dvw;
  align-items: center;
}

/* 反馈按钮 */
.feedback-button {
  display: flex;
  align-items: center;
  gap: 0.8dvw;
  padding: 0.6dvw 1.2dvw;
  font-size: 0.7dvw;
  color: white;
  border: none;
  border-radius: 0.4dvw;
  background: linear-gradient(135deg, #a0a0a0 0%, #808080 100%);
  backdrop-filter: blur(15px);
  transition:
    background 0.3s ease,
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.slider.collapsed .feedback-button {
  width: 2dvw;
  height: 2dvw;
  padding: 0;
  gap: 0;
  border-radius: 0.3dvw;
  min-height: auto;
}

.feedback-button:hover {
  color: white;
  background: linear-gradient(135deg, #b0b0b0 0%, #909090 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(160, 160, 160, 0.4);
}

/* 头部折叠按钮 */
.collapse-button-header {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5dvw;
  height: 1.5dvw;
  padding: 0;
  color: rgba(51, 51, 51, 0.7);
  background: transparent;
  border: none;
  border-radius: 0.3dvw;
  transition: all 0.3s ease;
  margin: 0;
  font-size: 20px;
  min-width: 28px;
}

.collapse-button-header:hover {
  color: #374151;
  background: #f3f4f6;
  border-radius: 4px;
}

.slider.collapsed .collapse-button-header {
  width: 24px;
  height: 24px;
  justify-content: center;
}

/* 折叠状态下按钮图标统一大小 */
.slider.collapsed .feedback-button .el-icon {
  font-size: 1dvw;
}

.app-user-info {
  display: flex;
  align-items: center;
  gap: 0.6dvw;
  font-weight: bold;
  font-size: 0.7dvw;
}

.user-dropdown {
  cursor: pointer;
}

.user-avatar {
  width: 3.5dvh;
  height: 3.5dvh;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.user-avatar:hover {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.user-dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 11dvw;
  gap: 8px;
  display: flex;
  flex-direction: column;
}

.menu-text {
  margin-inline: 0.8dvw;
}
